import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

const CreateFollowerSchema = z.object({
  instagramNickname: z.string().min(1),
  instagramId: z.string().optional(),
  avatar: z.string().url().optional(),
  followerCount: z.number().int().min(0).optional(),
  isVerified: z.boolean().default(false)
});

const BatchUploadSchema = z.object({
  followers: z.array(CreateFollowerSchema).min(1).max(1000),
  batchNumber: z.number().int().min(1).optional(),
  prioritizeNew: z.boolean().default(false)
});

/**
 * Upload Instagram followers in batch from Chrome extension
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = BatchUploadSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      },
      include: {
        organization: true
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Determine batch number
    let batchNumber = validatedData.batchNumber;
    if (!batchNumber) {
      const latestBatch = await prisma.instagramFollower.findFirst({
        where: {
          organizationId: membership.organizationId
        },
        select: { batchNumber: true },
        orderBy: { batchNumber: 'desc' }
      });
      batchNumber = (latestBatch?.batchNumber || 0) + 1;
    }

    // Check for existing followers to avoid duplicates
    const existingNicknames = await prisma.instagramFollower.findMany({
      where: {
        organizationId: membership.organizationId,
        instagramNickname: {
          in: validatedData.followers.map(f => f.instagramNickname)
        }
      },
      select: { instagramNickname: true }
    });

    const existingSet = new Set(existingNicknames.map(f => f.instagramNickname));
    const newFollowers = validatedData.followers.filter(
      f => !existingSet.has(f.instagramNickname)
    );

    if (newFollowers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All followers already exist',
        created: 0,
        skipped: validatedData.followers.length,
        batchNumber
      });
    }

    // Create new followers
    const followersToCreate = newFollowers.map(follower => ({
      userId: session.user.id,
      organizationId: membership.organizationId,
      instagramNickname: follower.instagramNickname,
      instagramId: follower.instagramId,
      avatar: follower.avatar,
      followerCount: follower.followerCount,
      isVerified: follower.isVerified,
      batchNumber,
      priority: validatedData.prioritizeNew ? 'high' : 'normal'
    }));

    const result = await prisma.instagramFollower.createMany({
      data: followersToCreate,
      skipDuplicates: true
    });

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${result.count} followers`,
      created: result.count,
      skipped: validatedData.followers.length - newFollowers.length,
      batchNumber
    });

  } catch (error) {
    console.error('Error uploading followers:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid data format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get Instagram followers with pagination and filtering
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const batchNumber = searchParams.get('batchNumber');

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const where: any = {
      organizationId: membership.organizationId
    };

    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (batchNumber) where.batchNumber = parseInt(batchNumber);

    const [followers, total] = await Promise.all([
      prisma.instagramFollower.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      }),
      prisma.instagramFollower.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: followers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching followers:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

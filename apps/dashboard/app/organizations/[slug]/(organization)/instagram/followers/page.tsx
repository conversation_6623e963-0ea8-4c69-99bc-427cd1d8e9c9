import * as React from 'react';
import { type Metadata } from 'next';

import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageSecondaryBar
} from '@workspace/ui/components/page';
import { EmptyState } from '@workspace/ui/components/empty-state';

import { getInstagramFollowers } from '~/data/instagram/get-instagram-followers';
import { InstagramFollowersTable } from '~/components/organizations/slug/instagram/followers/instagram-followers-table';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Instagram Followers')
};

export default async function InstagramFollowersPage(): Promise<React.JSX.Element> {
  const followers = await getInstagramFollowers();
  const hasFollowers = followers.length > 0;

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Instagram Followers"
              info="Manage followers captured from your Chrome extension"
            />
          </PagePrimaryBar>
          <PageSecondaryBar>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label htmlFor="status-filter" className="text-sm font-medium">
                  Status:
                </label>
                <select
                  id="status-filter"
                  className="rounded-md border border-input bg-background px-3 py-1 text-sm"
                >
                  <option value="">All</option>
                  <option value="pending">Pending</option>
                  <option value="contacted">Contacted</option>
                  <option value="responded">Responded</option>
                  <option value="engaged">Engaged</option>
                  <option value="converted">Converted</option>
                  <option value="ignored">Ignored</option>
                  <option value="blocked">Blocked</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <label htmlFor="priority-filter" className="text-sm font-medium">
                  Priority:
                </label>
                <select
                  id="priority-filter"
                  className="rounded-md border border-input bg-background px-3 py-1 text-sm"
                >
                  <option value="">All</option>
                  <option value="urgent">Urgent</option>
                  <option value="high">High</option>
                  <option value="normal">Normal</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>
          </PageSecondaryBar>
        </PageHeader>
        <PageBody className="p-6" disableScroll={hasFollowers}>
          {!hasFollowers ? (
            <EmptyState
              title="No followers yet"
              description="Followers will appear here once uploaded from your Chrome extension."
              icon="users"
            />
          ) : (
            <React.Suspense>
              <InstagramFollowersTable followers={followers} />
            </React.Suspense>
          )}
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}

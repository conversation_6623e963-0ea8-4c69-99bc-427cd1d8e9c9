import {
  BellIcon,
  BarChart3Icon,
  ClockIcon,
  CodeIcon,
  CreditCardIcon,
  HomeIcon,
  InstagramIcon,
  LockKeyholeIcon,
  PaletteIcon,
  ScrollTextIcon,
  SettingsIcon,
  ShieldIcon,
  StoreIcon,
  UserIcon,
  UserPlus2Icon,
  WrenchIcon
} from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

import { replaceOrgSlug, routes } from '@workspace/routes';

type NavItem = {
  title: string;
  href: string;
  disabled?: boolean;
  external?: boolean;
  icon: LucideIcon;
};

/**
 * Creates the main navigation items for the application sidebar.
 * Includes Instagram-related items and admin-only items for SaaS admin.
 *
 * @param slug - Organization slug for URL generation
 * @param isAdmin - Whether the user is an admin (currently unused)
 * @param isSaasAdmin - Whether the user is the SaaS admin (<EMAIL>)
 * @returns Array of navigation items
 */
export function createMainNavItems(slug: string, isAdmin: boolean = false, isSaasAdmin: boolean = false): NavItem[] {
  const baseItems = [
    {
      title: 'Home',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.Home, slug),
      icon: HomeIcon
    },
    {
      title: 'Instagram Contacts',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Contacts, slug),
      icon: InstagramIcon
    },
    {
      title: 'Instagram Followers',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Followers, slug),
      icon: UserIcon
    },
    {
      title: 'Instagram Statistics',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Statistics, slug),
      icon: BarChart3Icon
    },
    {
      title: 'Instagram Follow-ups',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.FollowUps, slug),
      icon: ClockIcon
    },
    {
      title: 'Instagram Prompts',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Prompts, slug),
      icon: ScrollTextIcon
    },
    {
      title: 'Instagram Settings',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Settings, slug),
      icon: SettingsIcon
    }
  ];

  // Add test settings for all users
  const testItems = [
    {
      title: 'Test your settings',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.TestSettings, slug),
      icon: WrenchIcon
    }
  ];

  // Add admin-only items (only for SaaS admin: <EMAIL>)
  // These items provide access to system-wide configuration and management
  const adminItems = isSaasAdmin ? [
    {
      title: 'Instagram Admin Prompt',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.AdminPrompt, slug),
      icon: WrenchIcon
    },
    {
      title: 'Instagram Bot Styles',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.instagram.BotStyles, slug),
      icon: PaletteIcon
    },
    {
      title: 'Instagram Admin Settings',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.admin.Settings, slug),
      icon: ShieldIcon
    }
  ] : [];

  return [...baseItems, ...testItems, ...adminItems];
}

/**
 * Creates navigation items for account settings.
 *
 * @param slug - Organization slug for URL generation
 * @returns Array of account navigation items
 */
export function createAccountNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'Profile',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Profile,
        slug
      ),
      icon: UserIcon
    },
    {
      title: 'Security',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Security,
        slug
      ),
      icon: LockKeyholeIcon
    },
    {
      title: 'Notifications',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Notifications,
        slug
      ),
      icon: BellIcon
    }
  ];
}

/**
 * Creates navigation items for organization settings.
 *
 * @param slug - Organization slug for URL generation
 * @returns Array of organization navigation items
 */
export function createOrganizationNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'General',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.General,
        slug
      ),
      icon: StoreIcon
    },
    {
      title: 'Members',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Members,
        slug
      ),
      icon: UserPlus2Icon
    },
    {
      title: 'Billing',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Billing,
        slug
      ),
      icon: CreditCardIcon
    },
    {
      title: 'Instagram',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Instagram,
        slug
      ),
      icon: InstagramIcon
    },
    {
      title: 'Developers',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Developers,
        slug
      ),
      icon: CodeIcon
    }
  ];
}

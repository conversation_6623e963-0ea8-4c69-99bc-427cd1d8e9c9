'use client';

import * as React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Progress } from '@workspace/ui/components/progress';
import { Separator } from '@workspace/ui/components/separator';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { Users, Bot, Target, TrendingUp } from 'lucide-react';

interface InstagramFollowersCardProps {
  stats: {
    total: number;
    automationEnabled: number;
    latestBatch: number;
    statusBreakdown: Record<string, number>;
  };
}

export function InstagramFollowersCard({ stats }: InstagramFollowersCardProps): React.JSX.Element {
  const params = useParams<{ slug: string }>();

  const automationPercentage = stats.total > 0 ? (stats.automationEnabled / stats.total) * 100 : 0;
  const contactedCount = stats.statusBreakdown.contacted || 0;
  const respondedCount = stats.statusBreakdown.responded || 0;
  const engagedCount = stats.statusBreakdown.engaged || 0;
  const convertedCount = stats.statusBreakdown.converted || 0;

  const responseRate = contactedCount > 0 ? ((respondedCount / contactedCount) * 100) : 0;
  const conversionRate = respondedCount > 0 ? ((convertedCount / respondedCount) * 100) : 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Instagram Followers</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Total Followers */}
          <div>
            <div className="text-2xl font-bold">{stats.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total followers tracked
            </p>
          </div>

          <Separator />

          {/* Automation Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                <span>Automation Enabled</span>
              </div>
              <span className="font-medium">{stats.automationEnabled}</span>
            </div>
            <Progress value={automationPercentage} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {automationPercentage.toFixed(1)}% of followers have automation enabled
            </p>
          </div>

          <Separator />

          {/* Performance Metrics */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm font-medium">
              <TrendingUp className="h-4 w-4" />
              <span>Performance</span>
            </div>
            
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <div className="font-medium">{responseRate.toFixed(1)}%</div>
                <div className="text-xs text-muted-foreground">Response Rate</div>
              </div>
              <div>
                <div className="font-medium">{conversionRate.toFixed(1)}%</div>
                <div className="text-xs text-muted-foreground">Conversion Rate</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Status Breakdown */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Target className="h-4 w-4" />
              <span>Status Breakdown</span>
            </div>
            
            <div className="flex flex-wrap gap-1">
              {Object.entries(stats.statusBreakdown).map(([status, count]) => (
                <Badge key={status} variant="outline" className="text-xs">
                  {status}: {count}
                </Badge>
              ))}
            </div>
          </div>

          {/* Latest Batch Info */}
          {stats.latestBatch > 0 && (
            <>
              <Separator />
              <div className="text-sm">
                <span className="text-muted-foreground">Latest batch: </span>
                <span className="font-medium">#{stats.latestBatch}</span>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button asChild size="sm" className="flex-1">
              <Link href={replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Followers, params.slug)}>
                View All
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm" className="flex-1">
              <Link href={replaceOrgSlug(routes.dashboard.organizations.slug.instagram.FollowersUpload, params.slug)}>
                Upload
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

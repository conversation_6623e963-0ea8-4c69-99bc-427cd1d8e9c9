'use client';

import * as React from 'react';
import { format } from 'date-fns';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table';

import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { Checkbox } from '@workspace/ui/components/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@workspace/ui/components/dropdown-menu';
import { Input } from '@workspace/ui/components/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@workspace/ui/components/table';
import { ArrowUpDown, ChevronDown, MoreHorizontal, Shield, Users } from 'lucide-react';

import type { InstagramFollowerDto } from '~/types/dtos/instagram-follower-dto';
import { updateFollowerPriority, toggleFollowerAutomation } from '~/actions/instagram/upload-followers';
import { useToast } from '@workspace/ui/hooks/use-toast';

interface InstagramFollowersTableProps {
  followers: InstagramFollowerDto[];
}

export function InstagramFollowersTable({ followers: initialFollowers }: InstagramFollowersTableProps): React.JSX.Element {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [followers, setFollowers] = React.useState(initialFollowers);
  const { toast } = useToast();

  const handlePriorityChange = async (followerId: string, priority: 'low' | 'normal' | 'high' | 'urgent') => {
    const result = await updateFollowerPriority(followerId, priority);
    if (result.success) {
      setFollowers(prev => prev.map(f => 
        f.id === followerId ? { ...f, priority } : f
      ));
      toast({
        title: 'Priority updated',
        description: `Follower priority changed to ${priority}`
      });
    } else {
      toast({
        title: 'Error',
        description: result.error || 'Failed to update priority',
        variant: 'destructive'
      });
    }
  };

  const handleAutomationToggle = async (followerId: string, enabled: boolean) => {
    const result = await toggleFollowerAutomation(followerId, enabled);
    if (result.success) {
      setFollowers(prev => prev.map(f => 
        f.id === followerId ? { ...f, automationEnabled: enabled } : f
      ));
      toast({
        title: 'Automation updated',
        description: `Automation ${enabled ? 'enabled' : 'disabled'} for follower`
      });
    } else {
      toast({
        title: 'Error',
        description: result.error || 'Failed to toggle automation',
        variant: 'destructive'
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'default';
      case 'normal': return 'secondary';
      case 'low': return 'outline';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'converted': return 'default';
      case 'engaged': return 'secondary';
      case 'responded': return 'outline';
      case 'contacted': return 'outline';
      case 'pending': return 'secondary';
      case 'ignored': return 'destructive';
      case 'blocked': return 'destructive';
      default: return 'secondary';
    }
  };

  const columns: ColumnDef<InstagramFollowerDto>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false
    },
    {
      accessorKey: 'instagramNickname',
      header: 'Follower',
      cell: ({ row }) => {
        const follower = row.original;
        const username = follower.instagramNickname || 'Unknown';
        const fallbackText = username.length >= 2 ? username.substring(0, 2).toUpperCase() : 'UN';

        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-9 w-9">
              <AvatarImage src={follower.avatar || undefined} alt={username} />
              <AvatarFallback>{fallbackText}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{username}</span>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {follower.isVerified && <Shield className="h-3 w-3" />}
                {follower.followerCount && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {follower.followerCount.toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      }
    },
    {
      accessorKey: 'priority',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Priority
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const priority = row.getValue('priority') as string;
        return (
          <Badge variant={getPriorityColor(priority)} className="capitalize">
            {priority}
          </Badge>
        );
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge variant={getStatusColor(status)} className="capitalize">
            {status}
          </Badge>
        );
      }
    },
    {
      accessorKey: 'batchNumber',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Batch
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <span className="font-mono">#{row.getValue('batchNumber')}</span>;
      }
    },
    {
      accessorKey: 'automationEnabled',
      header: 'Automation',
      cell: ({ row }) => {
        const enabled = row.getValue('automationEnabled') as boolean;
        return (
          <Badge variant={enabled ? 'default' : 'outline'}>
            {enabled ? 'Enabled' : 'Disabled'}
          </Badge>
        );
      }
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Added
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return format(row.getValue('createdAt'), 'MMM dd, yyyy');
      }
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const follower = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleAutomationToggle(follower.id, !follower.automationEnabled)}
              >
                {follower.automationEnabled ? 'Disable' : 'Enable'} automation
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Set Priority</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handlePriorityChange(follower.id, 'urgent')}>
                Urgent
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handlePriorityChange(follower.id, 'high')}>
                High
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handlePriorityChange(follower.id, 'normal')}>
                Normal
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handlePriorityChange(follower.id, 'low')}>
                Low
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ];

  const table = useReactTable({
    data: followers,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection
    }
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter followers..."
          value={(table.getColumn('instagramNickname')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('instagramNickname')?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuItem
                    key={column.id}
                    className="capitalize"
                    onClick={() => column.toggleVisibility(!column.getIsVisible())}
                  >
                    <Checkbox
                      checked={column.getIsVisible()}
                      className="mr-2"
                    />
                    {column.id}
                  </DropdownMenuItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}

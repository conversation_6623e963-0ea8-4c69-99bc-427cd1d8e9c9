import { cache } from 'react';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database';

import type { InstagramFollowerDto } from '~/types/dtos/instagram-follower-dto';

export const getInstagramFollowers = cache(async (): Promise<InstagramFollowerDto[]> => {
  const ctx = await getAuthOrganizationContext();

  const followers = await prisma.instagramFollower.findMany({
    where: {
      organizationId: ctx.organization.id
    },
    select: {
      id: true,
      userId: true,
      organizationId: true,
      instagramNickname: true,
      instagramId: true,
      avatar: true,
      followerCount: true,
      isVerified: true,
      batchNumber: true,
      priority: true,
      status: true,
      isTargeted: true,
      lastResponseAt: true,
      conversationStartedAt: true,
      followUpCount: true,
      lastFollowUpAt: true,
      nextFollowUpAt: true,
      isConversationActive: true,
      automationEnabled: true,
      notes: true,
      createdAt: true,
      updatedAt: true
    },
    orderBy: [
      { priority: 'desc' },
      { createdAt: 'desc' }
    ]
  });

  return followers.map(follower => ({
    ...follower,
    priority: follower.priority as 'low' | 'normal' | 'high' | 'urgent',
    status: follower.status as 'pending' | 'contacted' | 'responded' | 'engaged' | 'converted' | 'ignored' | 'blocked'
  }));
});

export const getInstagramFollowerStats = cache(async () => {
  const ctx = await getAuthOrganizationContext();

  const stats = await prisma.instagramFollower.groupBy({
    by: ['status'],
    where: {
      organizationId: ctx.organization.id
    },
    _count: {
      id: true
    }
  });

  const totalFollowers = await prisma.instagramFollower.count({
    where: {
      organizationId: ctx.organization.id
    }
  });

  const automationEnabled = await prisma.instagramFollower.count({
    where: {
      organizationId: ctx.organization.id,
      automationEnabled: true
    }
  });

  const latestBatch = await prisma.instagramFollower.findFirst({
    where: {
      organizationId: ctx.organization.id
    },
    select: {
      batchNumber: true
    },
    orderBy: {
      batchNumber: 'desc'
    }
  });

  return {
    total: totalFollowers,
    automationEnabled,
    latestBatch: latestBatch?.batchNumber || 0,
    statusBreakdown: stats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.id;
      return acc;
    }, {} as Record<string, number>)
  };
});

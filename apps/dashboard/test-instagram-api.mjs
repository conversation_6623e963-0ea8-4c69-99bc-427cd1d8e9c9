#!/usr/bin/env node

// Test script for Instagram followers API
import { config } from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
config();

console.log('🚀 Testing Instagram Followers API\n');

// Test 1: Check if development server is running
async function testServerStatus() {
  console.log('🔍 Testing development server status...');
  
  try {
    const response = await fetch('http://localhost:3000/api/health', {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      console.log('✅ Development server is running');
      return true;
    } else {
      console.log(`⚠️  Server responded with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Development server is not running on localhost:3000');
      console.log('   Please start it with: npm run dev');
      return false;
    } else {
      console.log(`❌ Server test failed: ${error.message}`);
      return false;
    }
  }
}

// Test 2: Test Instagram followers API endpoint
async function testInstagramFollowersAPI() {
  console.log('\n🔍 Testing Instagram followers API endpoint...');
  
  try {
    const response = await fetch('http://localhost:3000/api/instagram/followers', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000
    });
    
    console.log(`📡 Response Status: ${response.status}`);
    
    const responseText = await response.text();
    
    if (response.status === 401) {
      console.log('✅ API endpoint is accessible (401 Unauthorized expected without auth)');
      try {
        const data = JSON.parse(responseText);
        console.log('📄 Response:', JSON.stringify(data, null, 2));
      } catch {
        console.log('📄 Response:', responseText);
      }
      return true;
    } else if (response.status === 500) {
      console.log('❌ Internal Server Error - this indicates the database issue might still exist');
      console.log('📄 Error Response:', responseText);
      return false;
    } else {
      console.log('📄 Response:', responseText);
      return response.ok;
    }
    
  } catch (error) {
    console.log(`❌ API test failed: ${error.message}`);
    return false;
  }
}

// Test 3: Test with a simple POST to see if the endpoint handles requests
async function testInstagramFollowersPost() {
  console.log('\n🔍 Testing Instagram followers POST endpoint...');
  
  try {
    const testData = {
      followers: [
        {
          instagramNickname: "test_user_" + Date.now(),
          instagramId: "test_id_" + Date.now(),
          avatar: "https://example.com/avatar.jpg",
          followerCount: 1000,
          isVerified: false
        }
      ],
      prioritizeNew: false
    };
    
    const response = await fetch('http://localhost:3000/api/instagram/followers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
      timeout: 10000
    });
    
    console.log(`📡 POST Response Status: ${response.status}`);
    
    const responseText = await response.text();
    
    if (response.status === 401) {
      console.log('✅ POST endpoint is accessible (401 Unauthorized expected without auth)');
    } else if (response.status === 500) {
      console.log('❌ Internal Server Error on POST');
      console.log('📄 Error Response:', responseText);
      return false;
    }
    
    try {
      const data = JSON.parse(responseText);
      console.log('📄 Response:', JSON.stringify(data, null, 2));
    } catch {
      console.log('📄 Response:', responseText);
    }
    
    return true;
    
  } catch (error) {
    console.log(`❌ POST test failed: ${error.message}`);
    return false;
  }
}

// Test 4: Check if we can access the page directly
async function testInstagramFollowersPage() {
  console.log('\n🔍 Testing Instagram followers page...');
  
  try {
    const response = await fetch('http://localhost:3000/organizations/alexgodlewsky/instagram/followers', {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      },
      timeout: 15000
    });
    
    console.log(`📡 Page Response Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ Instagram followers page loads successfully');
      return true;
    } else if (response.status === 500) {
      const responseText = await response.text();
      if (responseText.includes('instagramFollower')) {
        console.log('❌ Page still has the instagramFollower error');
        return false;
      } else {
        console.log('❌ Page returned 500 error for different reason');
        console.log('📄 Error snippet:', responseText.substring(0, 200) + '...');
        return false;
      }
    } else {
      console.log(`⚠️  Page returned status: ${response.status}`);
      return response.status < 500; // 4xx errors are ok (auth issues), 5xx are not
    }
    
  } catch (error) {
    console.log(`❌ Page test failed: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  const results = [];
  
  results.push(await testServerStatus());
  results.push(await testInstagramFollowersAPI());
  results.push(await testInstagramFollowersPost());
  results.push(await testInstagramFollowersPage());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! The Instagram followers API is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the output above for details.');
  }
  
  return passed === total;
}

runTests().catch(console.error);

export interface InstagramFollowerDto {
  id: string;
  userId: string;
  organizationId: string;
  instagramNickname: string;
  instagramId?: string;
  avatar?: string;
  followerCount?: number;
  isVerified: boolean;
  batchNumber: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'contacted' | 'responded' | 'engaged' | 'converted' | 'ignored' | 'blocked';
  isTargeted: boolean;
  lastResponseAt?: Date;
  conversationStartedAt?: Date;
  followUpCount: number;
  lastFollowUpAt?: Date;
  nextFollowUpAt?: Date;
  isConversationActive: boolean;
  automationEnabled: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateInstagramFollowerDto {
  instagramNickname: string;
  instagramId?: string;
  avatar?: string;
  followerCount?: number;
  isVerified?: boolean;
  batchNumber?: number;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface UpdateInstagramFollowerDto {
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  status?: 'pending' | 'contacted' | 'responded' | 'engaged' | 'converted' | 'ignored' | 'blocked';
  isTargeted?: boolean;
  automationEnabled?: boolean;
  notes?: string;
}

export interface InstagramFollowerBatchDto {
  followers: CreateInstagramFollowerDto[];
  batchNumber: number;
  prioritizeNew?: boolean;
}

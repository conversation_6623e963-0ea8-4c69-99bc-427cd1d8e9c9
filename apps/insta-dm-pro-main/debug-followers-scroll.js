// Instagram Followers Scraping Debug Script
// Paste this into the browser console when you have the followers dialog open

console.log('🐛 Starting Instagram Followers Scraping Debug...');

// Find the followers dialog
const dialog = document.querySelector('[role="dialog"]');
if (!dialog) {
    console.error('❌ No followers dialog found! Make sure you have the followers popup open.');
    throw new Error('Dialog not found');
}

console.log('✅ Dialog found:', dialog);

// Find scrollable elements within the dialog
function findScrollableElements(root) {
    const scrollableElements = [];

    function checkElement(el) {
        const style = window.getComputedStyle(el);
        if (
            (style.overflowY === 'scroll' || style.overflowY === 'auto') &&
            el.scrollHeight > el.clientHeight
        ) {
            scrollableElements.push(el);
        }
    }

    function traverse(el) {
        if (el.children.length) {
            Array.from(el.children).forEach((child) => {
                checkElement(child);
                traverse(child);
            });
        }
    }

    traverse(root);
    return scrollableElements;
}

// Enhanced function to find the actual followers container
function findFollowersScrollContainer(dialog) {
    console.log('🔍 Looking for followers scroll container...');

    // Method 1: Look for elements with overflow-y: scroll in computed styles
    // This targets the specific element we saw in the DOM inspector
    const allDivs = dialog.querySelectorAll('div');
    console.log('📋 Total divs in dialog:', allDivs.length);

    for (let div of allDivs) {
        const computedStyle = window.getComputedStyle(div);

        // Look for the element with overflow-y: scroll
        if (computedStyle.overflowY === 'scroll') {
            console.log('🎯 Found element with overflow-y: scroll:', {
                element: div,
                className: div.className,
                scrollHeight: div.scrollHeight,
                clientHeight: div.clientHeight,
                overflowY: computedStyle.overflowY,
                canScroll: div.scrollHeight > div.clientHeight,
                maxHeight: computedStyle.maxHeight
            });

            // Check if this element actually has scrollable content
            if (div.scrollHeight > div.clientHeight) {
                console.log('✅ Found scrollable element with overflow-y: scroll:', div);
                return div;
            }
        }
    }

    // Method 2: Look for elements with max-height and overflow-y: scroll
    for (let div of allDivs) {
        const computedStyle = window.getComputedStyle(div);

        if (computedStyle.maxHeight && computedStyle.maxHeight !== 'none' &&
            (computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto')) {
            console.log('🎯 Found element with max-height and overflow:', {
                element: div,
                className: div.className,
                scrollHeight: div.scrollHeight,
                clientHeight: div.clientHeight,
                overflowY: computedStyle.overflowY,
                maxHeight: computedStyle.maxHeight,
                canScroll: div.scrollHeight > div.clientHeight
            });

            if (div.scrollHeight > div.clientHeight) {
                console.log('✅ Found scrollable element with max-height:', div);
                return div;
            }
        }
    }

    // Method 3: Look for the third container in containers with max-height (as user suggested)
    const containersWithMaxHeight = [];
    for (let div of allDivs) {
        const computedStyle = window.getComputedStyle(div);
        if (computedStyle.maxHeight && computedStyle.maxHeight !== 'none') {
            containersWithMaxHeight.push(div);
        }
    }

    console.log('📋 Found containers with max-height:', containersWithMaxHeight.length);

    if (containersWithMaxHeight.length >= 3) {
        const thirdContainer = containersWithMaxHeight[2]; // Third container (0-indexed)
        console.log('🎯 Checking third container with max-height:', {
            element: thirdContainer,
            className: thirdContainer.className,
            scrollHeight: thirdContainer.scrollHeight,
            clientHeight: thirdContainer.clientHeight,
            maxHeight: window.getComputedStyle(thirdContainer).maxHeight
        });

        if (thirdContainer.scrollHeight > thirdContainer.clientHeight) {
            console.log('✅ Using third container with max-height:', thirdContainer);
            return thirdContainer;
        }
    }

    // Method 4: Look for elements with specific class patterns that indicate scrollable content
    const scrollableClassPatterns = [
        'xyi19xy', // From the DOM inspector
        'x1ccrb07',
        'xtf3nb5'
    ];

    for (let pattern of scrollableClassPatterns) {
        const elements = dialog.querySelectorAll(`div[class*="${pattern}"]`);
        console.log(`📋 Found ${elements.length} elements with class containing "${pattern}"`);

        for (let element of elements) {
            const computedStyle = window.getComputedStyle(element);
            if ((computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto') &&
                element.scrollHeight > element.clientHeight) {
                console.log('✅ Found scrollable element by class pattern:', element);
                return element;
            }
        }
    }

    // Method 5: Fallback to standard scrollable element detection
    console.log('🔄 Falling back to standard scrollable element detection...');
    return findScrollableElements(dialog)[0];
}

// Use the enhanced function to find the correct container
const scrollingEl = findFollowersScrollContainer(dialog);

if (!scrollingEl) {
    console.error('❌ No followers scroll container found!');
    throw new Error('No followers scroll container found');
}

console.log('🎯 Using followers scroll container:', scrollingEl);
console.log('📊 Container details:', {
    tagName: scrollingEl.tagName,
    className: scrollingEl.className,
    scrollHeight: scrollingEl.scrollHeight,
    clientHeight: scrollingEl.clientHeight,
    scrollTop: scrollingEl.scrollTop,
    canScroll: scrollingEl.scrollHeight > scrollingEl.clientHeight,
    style: scrollingEl.getAttribute('style')
});

// Enhanced scrolling function with multiple methods
async function aggressiveScroll(element, distance = 500) {
    const beforeScroll = element.scrollTop;
    console.log(`📜 Scrolling ${distance}px from position ${beforeScroll}`);

    // Method 1: scrollBy with smooth behavior
    element.scrollBy({
        top: distance,
        behavior: 'smooth'
    });

    // Wait for scroll to complete
    await new Promise(resolve => setTimeout(resolve, 300));

    let afterScroll = element.scrollTop;
    let actualScrolled = afterScroll - beforeScroll;

    // Method 2: Direct scrollTop if scrollBy didn't work
    if (actualScrolled === 0) {
        console.log('🔄 Trying direct scrollTop...');
        element.scrollTop += distance;
        await new Promise(resolve => setTimeout(resolve, 200));
        afterScroll = element.scrollTop;
        actualScrolled = afterScroll - beforeScroll;
    }

    // Method 3: Try scrolling parent element
    if (actualScrolled === 0 && element.parentElement) {
        console.log('🔄 Trying parent element scroll...');
        const parent = element.parentElement;
        const parentBefore = parent.scrollTop;
        parent.scrollBy({ top: distance, behavior: 'smooth' });
        await new Promise(resolve => setTimeout(resolve, 300));
        const parentAfter = parent.scrollTop;
        const parentMoved = parentAfter - parentBefore;

        if (parentMoved > 0) {
            console.log(`✅ Parent scrolled: ${parentBefore} → ${parentAfter} (moved ${parentMoved}px)`);
            return {
                before: parentBefore,
                after: parentAfter,
                moved: parentMoved,
                reachedBottom: parentAfter + parent.clientHeight >= parent.scrollHeight - 10,
                scrolledElement: parent
            };
        }
    }

    // Method 4: Try wheel event simulation
    if (actualScrolled === 0) {
        console.log('🔄 Trying wheel event simulation...');
        const wheelEvent = new WheelEvent('wheel', {
            deltaY: distance,
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(wheelEvent);
        await new Promise(resolve => setTimeout(resolve, 300));
        afterScroll = element.scrollTop;
        actualScrolled = afterScroll - beforeScroll;
    }

    console.log(`📊 Final scroll result: ${beforeScroll} → ${afterScroll} (moved ${actualScrolled}px)`);

    return {
        before: beforeScroll,
        after: afterScroll,
        moved: actualScrolled,
        reachedBottom: afterScroll + element.clientHeight >= element.scrollHeight - 10,
        scrolledElement: element
    };
}

// Function to extract follower links
function extractFollowerLinks(scrollingEl) {
    const profileLinks = new Set();

    // Try multiple DOM paths to find followers
    const possiblePaths = [
        scrollingEl.children[0]?.children[0]?.children,
        scrollingEl.children[0]?.children,
        scrollingEl.children[1]?.children[0]?.children,
        scrollingEl.children[1]?.children,
        scrollingEl.querySelectorAll('a[href^="/"][href*="/"]')
    ];

    console.log('🔍 Checking possible DOM paths for followers...');

    for (let i = 0; i < possiblePaths.length; i++) {
        const path = possiblePaths[i];
        if (path && path.length > 0) {
            console.log(`✅ Path ${i} found ${path.length} elements`);

            for (let follower of path) {
                let link = follower.querySelector ? follower.querySelector('a')?.href : follower.href;
                if (link && link.includes('instagram.com/') && !link.includes('/followers') && !link.includes('/following')) {
                    profileLinks.add(link);
                }
            }

            if (profileLinks.size > 0) {
                console.log(`🎯 Using path ${i}, found ${profileLinks.size} profile links`);
                break;
            }
        } else {
            console.log(`❌ Path ${i} is empty or null`);
        }
    }

    return Array.from(profileLinks);
}

// Main scraping function
async function testFollowersScraping(targetCount = 20) {
    console.log(`🚀 Starting followers scraping test (target: ${targetCount} followers)`);

    let allLinks = new Set();
    let scrollAttempts = 0;
    const maxAttempts = 20;
    let noProgressCount = 0;

    while (allLinks.size < targetCount && scrollAttempts < maxAttempts) {
        scrollAttempts++;
        console.log(`\n🔄 Attempt ${scrollAttempts}/${maxAttempts}`);

        // Extract current followers
        const currentLinks = extractFollowerLinks(scrollingEl);
        const previousSize = allLinks.size;

        currentLinks.forEach(link => allLinks.add(link));

        const newLinksFound = allLinks.size - previousSize;
        console.log(`📈 Progress: ${allLinks.size} total followers (+${newLinksFound} new)`);

        if (newLinksFound === 0) {
            noProgressCount++;
            console.log(`⚠️ No new followers found (${noProgressCount} times)`);

            if (noProgressCount >= 3) {
                console.log('🛑 No progress for 3 attempts, trying aggressive scroll...');
                const scrollResult = await aggressiveScroll(scrollingEl, 800);

                if (scrollResult.reachedBottom) {
                    console.log('🏁 Reached bottom of followers list');
                    break;
                }

                noProgressCount = 0; // Reset counter after aggressive scroll
            }
        } else {
            noProgressCount = 0; // Reset counter when we find new followers
        }

        // Regular scroll
        const scrollResult = await aggressiveScroll(scrollingEl, 300);

        if (scrollResult.reachedBottom) {
            console.log('🏁 Reached bottom of followers list');
            break;
        }

        // Wait for content to load
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`\n✅ Scraping completed!`);
    console.log(`📊 Results:`);
    console.log(`   - Total followers found: ${allLinks.size}`);
    console.log(`   - Scroll attempts: ${scrollAttempts}`);
    console.log(`   - Target reached: ${allLinks.size >= targetCount ? 'Yes' : 'No'}`);

    const links = Array.from(allLinks);
    console.log(`\n📋 First 10 followers:`, links.slice(0, 10));

    return links;
}

// Run the test
console.log('🎬 Starting test in 2 seconds...');
setTimeout(() => {
    testFollowersScraping(50).then(results => {
        console.log('🎉 Test completed! Results:', results.length, 'followers found');
        window.debugResults = results; // Store results globally
        console.log('💾 Results stored in window.debugResults');
    }).catch(error => {
        console.error('❌ Test failed:', error);
    });
}, 2000);
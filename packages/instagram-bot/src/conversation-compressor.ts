/**
 * Conversation compression utilities for Anthropic prompt caching
 * Compresses conversation history while maintaining context for AI understanding
 */

interface MessageData {
  isFromUser: boolean;
  content?: string;
  mediaDescription: string | null;
  mediaUrl: string | null;
  mediaType: string | null;
  timestamp?: Date;
}

interface CompressedConversation {
  compressedHistory: string;
  messageCount: number;
  compressionRatio: number;
  tokenEstimate: number;
}

/**
 * Compress conversation history for caching while maintaining AI readability
 * Limits to maximum number of messages and compresses older messages
 * @param messages Array of message data to compress
 * @param maxMessages Maximum number of messages to keep (should come from admin settings)
 */
export function compressConversationHistory(
  messages: MessageData[],
  maxMessages: number
): CompressedConversation {
  if (messages.length === 0) {
    return {
      compressedHistory: '',
      messageCount: 0,
      compressionRatio: 1,
      tokenEstimate: 0
    };
  }

  // Sort messages by timestamp if available, otherwise keep original order
  const sortedMessages = messages.sort((a, b) => {
    if (a.timestamp && b.timestamp) {
      return a.timestamp.getTime() - b.timestamp.getTime();
    }
    return 0;
  });

  // Take the most recent messages up to maxMessages
  const recentMessages = sortedMessages.slice(-maxMessages);

  // If we have more messages than the limit, create a summary of older messages
  let conversationSummary = '';
  if (sortedMessages.length > maxMessages) {
    const olderMessagesCount = sortedMessages.length - maxMessages;
    conversationSummary = `[Previous conversation summary: ${olderMessagesCount} earlier messages exchanged between User and Ja]\n\n`;
  }

  // Format recent messages in compressed but readable format
  const formattedMessages = recentMessages.map(msg => {
    const role = msg.isFromUser ? 'User' : 'Ja';
    let content = msg.content || '';

    // Compress media descriptions
    if (msg.mediaDescription) {
      content += ` [${msg.mediaType || 'media'}: ${msg.mediaDescription}]`;
    } else if (msg.mediaUrl && msg.mediaType) {
      content += ` [${msg.mediaType}]`;
    }

    // Remove excessive whitespace and normalize
    content = content.trim().replace(/\s+/g, ' ');

    return `${role}: ${content}`;
  });

  const compressedHistory = conversationSummary + formattedMessages.join('\n');

  // Calculate compression metrics
  const originalLength = messages.reduce((sum, msg) => sum + (msg.content?.length || 0), 0);
  const compressedLength = compressedHistory.length;
  const compressionRatio = originalLength > 0 ? compressedLength / originalLength : 1;

  // Rough token estimate (1 token ≈ 4 characters for English text)
  const tokenEstimate = Math.ceil(compressedLength / 4);

  return {
    compressedHistory,
    messageCount: recentMessages.length,
    compressionRatio,
    tokenEstimate
  };
}

/**
 * Check if conversation history meets minimum token requirements for caching
 * Claude Sonnet 4 requires minimum 1024 tokens for caching
 */
export function meetsMinimumCacheRequirements(
  systemPromptLength: number,
  conversationLength: number,
  model: string = 'claude-sonnet-4'
): boolean {
  const totalLength = systemPromptLength + conversationLength;
  const estimatedTokens = Math.ceil(totalLength / 4);

  // Minimum token requirements by model
  const minimumTokens = model.includes('haiku') ? 2048 : 1024;

  return estimatedTokens >= minimumTokens;
}

/**
 * Create cache-optimized conversation format
 * Structures conversation for optimal caching with proper cache_control placement
 */
export function createCacheOptimizedConversation(
  systemPrompt: string,
  conversationHistory: string,
  cacheType: '5m' | '1h' = '5m',
  enableConversationCache: boolean = true
): {
  systemMessage: any[];
  userMessage: string;
  cacheInfo: {
    systemCached: boolean;
    conversationCached: boolean;
    estimatedTokens: number;
  };
} {
  const systemTokens = Math.ceil(systemPrompt.length / 4);
  const conversationTokens = Math.ceil(conversationHistory.length / 4);
  const totalTokens = systemTokens + conversationTokens;

  // Determine cache control configuration
  const cacheControl = {
    type: 'ephemeral' as const,
    ...(cacheType === '1h' ? { ttl: '1h' as const } : {})
  };

  // Always cache system prompt if it meets minimum requirements
  const systemCached = systemTokens >= 512; // Lower threshold for system prompt

  // Cache conversation if enabled and meets requirements
  const conversationCached = enableConversationCache &&
    conversationTokens >= 512 &&
    totalTokens >= (systemPrompt.includes('haiku') ? 2048 : 1024);

  let systemMessage: any[];
  let userMessage: string;

  if (conversationCached) {
    // Cache both system prompt and conversation history
    systemMessage = [
      {
        type: 'text',
        text: systemPrompt,
        cache_control: cacheControl
      }
    ];

    userMessage = `Here is the conversation history:\n\n${conversationHistory}\n\nPlease respond to the user's last message.`;

    // Note: For conversation caching, we rely on the system prompt cache
    // The conversation history is included in the user message but not separately cached
    // to avoid complexity with message structure
  } else if (systemCached) {
    // Cache only system prompt
    systemMessage = [
      {
        type: 'text',
        text: systemPrompt,
        cache_control: cacheControl
      }
    ];

    userMessage = `Here is the conversation history:\n\n${conversationHistory}\n\nPlease respond to the user's last message.`;
  } else {
    // No caching
    systemMessage = [
      {
        type: 'text',
        text: systemPrompt
      }
    ];

    userMessage = `Here is the conversation history:\n\n${conversationHistory}\n\nPlease respond to the user's last message.`;
  }

  return {
    systemMessage,
    userMessage,
    cacheInfo: {
      systemCached,
      conversationCached,
      estimatedTokens: totalTokens
    }
  };
}

/**
 * Log cache performance metrics
 */
export function logCachePerformance(
  usage: any,
  cacheInfo: { systemCached: boolean; conversationCached: boolean; estimatedTokens: number }
): void {
  if (!usage) return;

  const {
    cache_creation_input_tokens = 0,
    cache_read_input_tokens = 0,
    input_tokens = 0
  } = usage;

  console.log('Cache Performance Metrics:', {
    cacheCreated: cache_creation_input_tokens,
    cacheRead: cache_read_input_tokens,
    uncachedInput: input_tokens,
    totalInput: cache_creation_input_tokens + cache_read_input_tokens + input_tokens,
    cacheHitRate: cache_read_input_tokens > 0 ?
      (cache_read_input_tokens / (cache_read_input_tokens + cache_creation_input_tokens + input_tokens)) * 100 : 0,
    systemCached: cacheInfo.systemCached,
    conversationCached: cacheInfo.conversationCached,
    estimatedTokens: cacheInfo.estimatedTokens
  });
}

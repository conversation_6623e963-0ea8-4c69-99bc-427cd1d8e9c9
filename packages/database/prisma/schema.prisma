generator client {
  provider        = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String   @id(map: "PK_Account") @default(uuid()) @db.Uuid
  userId            String   @db.Uuid
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "IX_Account_userId")
}

model ApiKey {
  id             String       @id(map: "PK_ApiKey") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  description    String       @db.VarChar(70)
  hashedKey      String       @unique
  expiresAt      DateTime?
  lastUsedAt     DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_ApiKey_organizationId")
}

model AuthenticatorApp {
  id            String   @id(map: "PK_AuthenticatorApp") @default(uuid()) @db.Uuid
  userId        String   @unique @db.Uuid
  accountName   String   @db.VarChar(255)
  issuer        String   @db.VarChar(255)
  secret        String   @db.VarChar(255)
  recoveryCodes String   @db.VarChar(1024)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_AuthenticatorApp_userId")
}

model ChangeEmailRequest {
  id        String   @id(map: "PK_ChangeEmailRequest") @default(uuid()) @db.Uuid
  userId    String   @db.Uuid
  email     String
  expires   DateTime
  valid     Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_ChangeEmailRequest_userId")
}

model Contact {
  id             String             @id(map: "PK_Contact") @default(uuid()) @db.Uuid
  organizationId String             @db.Uuid
  record         ContactRecord      @default(PERSON)
  image          String?            @db.VarChar(2048)
  name           String             @db.VarChar(255)
  email          String?            @db.VarChar(255)
  address        String?            @db.VarChar(255)
  phone          String?            @db.VarChar(32)
  stage          ContactStage       @default(LEAD)
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  organization   Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  activities     ContactActivity[]
  comments       ContactComment[]
  notes          ContactNote[]
  pageVisits     ContactPageVisit[]
  tasks          ContactTask[]
  favorites      Favorite[]
  tags           ContactTag[]       @relation("ContactToContactTag")

  @@index([organizationId], map: "IX_Contact_organizationId")
}

model ContactActivity {
  id         String     @id(map: "PK_ContactActivity") @default(uuid()) @db.Uuid
  contactId  String     @db.Uuid
  actionType ActionType
  actorId    String     @db.VarChar(255)
  actorType  ActorType
  metadata   Json?
  occurredAt DateTime   @default(now())
  contact    Contact    @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId], map: "IX_ContactActivity_contactId")
  @@index([occurredAt], map: "IX_ContactActivity_occurredAt")
}

model ContactComment {
  id        String   @id(map: "PK_ContactComment") @default(uuid()) @db.Uuid
  contactId String   @db.Uuid
  userId    String   @db.Uuid
  text      String   @db.VarChar(2000)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  contact   Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([contactId], map: "IX_ContactComment_contactId")
  @@index([userId], map: "IX_ContactComment_userId")
}

model ContactImage {
  id          String  @id(map: "PK_ContactImage") @default(uuid()) @db.Uuid
  contactId   String  @db.Uuid
  data        Bytes?
  contentType String? @db.VarChar(255)
  hash        String? @db.VarChar(64)

  @@index([contactId], map: "IX_ContactImage_contactId")
}

model ContactNote {
  id        String   @id(map: "PK_ContactNote") @default(uuid()) @db.Uuid
  contactId String   @db.Uuid
  userId    String   @db.Uuid
  text      String?  @db.VarChar(8000)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  contact   Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([contactId], map: "IX_ContactNote_contactId")
  @@index([userId], map: "IX_ContactNote_userId")
}

model ContactPageVisit {
  id        String   @id(map: "PK_ContactPageVisit") @default(uuid()) @db.Uuid
  contactId String   @db.Uuid
  userId    String?  @db.Uuid
  timestamp DateTime @default(now())
  contact   Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id])

  @@index([contactId], map: "IX_ContactPageVisit_contactId")
  @@index([userId], map: "IX_ContactPageVisit_userId")
}

model ContactTag {
  id       String    @id(map: "PK_ContactTag") @default(uuid()) @db.Uuid
  text     String    @unique @db.VarChar(128)
  contacts Contact[] @relation("ContactToContactTag")
}

model ContactTask {
  id          String            @id(map: "PK_ContactTask") @default(uuid()) @db.Uuid
  contactId   String            @db.Uuid
  title       String            @db.VarChar(255)
  description String?           @db.VarChar(8000)
  status      ContactTaskStatus @default(OPEN)
  dueDate     DateTime?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  contact     Contact           @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId], map: "IX_ContactTask_contactId")
}

model Favorite {
  id        String  @id(map: "PK_Favorite") @default(uuid()) @db.Uuid
  userId    String  @db.Uuid
  contactId String  @db.Uuid
  order     Int     @default(0)
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_Favorite_userId")
  @@index([contactId], map: "IX_Favorite_contactId")
}

model Feedback {
  id             String           @id(map: "PK_Feedback") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  userId         String?          @db.Uuid
  category       FeedbackCategory @default(SUGGESTION)
  message        String           @db.VarChar(4000)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?            @relation(fields: [userId], references: [id])

  @@index([organizationId], map: "IX_Feedback_organizationId")
  @@index([userId], map: "IX_Feedback_userId")
}

model Invitation {
  id             String           @id(map: "PK_Invitation") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  token          String           @default(uuid()) @db.Uuid
  email          String           @db.VarChar(255)
  role           Role             @default(MEMBER)
  status         InvitationStatus @default(PENDING)
  lastSentAt     DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Invitation_organizationId")
  @@index([token], map: "IX_Invitation_token")
}

model Membership {
  id             String       @id(map: "PK_Membership") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  userId         String       @db.Uuid
  role           Role         @default(MEMBER)
  isOwner        Boolean      @default(false)
  createdAt      DateTime     @default(now())
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
}

model Notification {
  id             String        @id(map: "PK_Notification") @default(uuid()) @db.Uuid
  userId         String        @db.Uuid
  subject        String?       @db.VarChar(128)
  content        String        @db.VarChar(8000)
  link           String?       @db.VarChar(2000)
  seenAt         DateTime?
  dismissed      Boolean       @default(false)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  organizationId String?       @db.Uuid
  Organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_Notification_userId")
  @@index([organizationId], map: "IX_Notification_organizationId")
}

model Organization {
  id                   String                 @id(map: "PK_Organization") @default(uuid()) @db.Uuid
  stripeCustomerId     String
  name                 String                 @db.VarChar(255)
  address              String?                @db.VarChar(255)
  phone                String?                @db.VarChar(32)
  email                String?                @db.VarChar(255)
  website              String?                @db.VarChar(2000)
  tier                 String                 @default("free") @db.VarChar(255)
  facebookPage         String?                @db.VarChar(2000)
  instagramProfile     String?                @db.VarChar(2000)
  linkedInProfile      String?                @db.VarChar(2000)
  tikTokProfile        String?                @db.VarChar(2000)
  xProfile             String?                @db.VarChar(2000)
  youTubeChannel       String?                @db.VarChar(2000)
  logo                 String?                @db.VarChar(2048)
  slug                 String                 @unique @db.VarChar(255)
  apiKeys              ApiKey[]
  contacts             Contact[]
  feedback             Feedback[]
  InstagramBotSettings InstagramBotSettings[]
  InstagramContact     InstagramContact[]
  InstagramError       InstagramError[]
  InstagramFollower    InstagramFollower[]
  InstagramSettings    InstagramSettings?
  invitations          Invitation[]
  memberships          Membership[]
  MessageQueue         MessageQueue[]
  Notification         Notification[]
  PromptConfig         PromptConfig[]
  webhooks             Webhook[]
  businessHours        WorkHours[]
  TestSettings         TestSettings[]
  TestAdminPrompt      TestAdminPrompt[]

  @@index([stripeCustomerId], map: "IX_Organization_stripeCustomerId")
}

model OrganizationLogo {
  id             String  @id(map: "PK_OrganizationLogo") @default(uuid()) @db.Uuid
  organizationId String  @db.Uuid
  data           Bytes?
  contentType    String? @db.VarChar(255)
  hash           String? @db.VarChar(64)

  @@index([organizationId], map: "IX_OrganizationLogo_organizationId")
}

model ResetPasswordRequest {
  id        String   @id(map: "PK_ResetPasswordRequest") @default(uuid()) @db.Uuid
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email], map: "IX_ResetPasswordRequest_email")
}

model Session {
  id           String   @id(map: "PK_Session") @default(uuid()) @db.Uuid
  sessionToken String   @unique
  userId       String   @db.Uuid
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "IX_Session_userId")
}

model User {
  id                           String                 @id(map: "PK_User") @default(uuid()) @db.Uuid
  image                        String?                @db.VarChar(2048)
  name                         String                 @db.VarChar(64)
  email                        String?                @unique
  emailVerified                DateTime?
  password                     String?                @db.VarChar(60)
  lastLogin                    DateTime?
  phone                        String?                @db.VarChar(32)
  locale                       String                 @default("en-US") @db.VarChar(8)
  completedOnboarding          Boolean                @default(false)
  enabledContactsNotifications Boolean                @default(false)
  enabledInboxNotifications    Boolean                @default(false)
  enabledWeeklySummary         Boolean                @default(false)
  enabledNewsletter            Boolean                @default(false)
  enabledProductUpdates        Boolean                @default(false)
  createdAt                    DateTime               @default(now())
  updatedAt                    DateTime               @updatedAt
  accounts                     Account[]
  authenticatorApp             AuthenticatorApp?
  changeEmailRequests          ChangeEmailRequest[]
  comments                     ContactComment[]
  notes                        ContactNote[]
  pageVisits                   ContactPageVisit[]
  favorites                    Favorite[]
  feedback                     Feedback[]
  InstagramBotSettings         InstagramBotSettings[]
  InstagramContact             InstagramContact[]
  InstagramFollower            InstagramFollower[]
  memberships                  Membership[]
  notifications                Notification[]
  sessions                     Session[]
  TestSettings                 TestSettings[]
  TestAdminPrompt              TestAdminPrompt[]
}

model UserImage {
  id          String  @id(map: "PK_UserImage") @default(uuid()) @db.Uuid
  userId      String  @db.Uuid
  data        Bytes?
  contentType String? @db.VarChar(255)
  hash        String? @db.VarChar(64)

  @@index([userId], map: "IX_UserImage_userId")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Webhook {
  id             String           @id(map: "PK_Webhook") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  url            String           @db.VarChar(2000)
  triggers       WebhookTrigger[]
  secret         String?          @db.VarChar(1024)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Webhook_organizationId")
}

model InstagramContact {
  id                    String                  @id @default(uuid()) @db.Uuid
  userId                String                  @db.Uuid
  organizationId        String                  @db.Uuid
  instagramNickname     String
  instagramId           String?
  firstName             String?
  lastName              String?
  email                 String?
  phone                 String?
  status                String                  @default("new")
  lastInteractionAt     DateTime?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  avatar                String?                 @db.VarChar(2048)
  followUpMessage1      String?
  followUpMessage2      String?
  followUpMessage3      String?
  followUpMessage4      String?
  followUpMessage5      String?
  followUpMessage6      String?
  followUpStatus1       FollowUpStatus?
  followUpStatus2       FollowUpStatus?
  followUpStatus3       FollowUpStatus?
  followUpStatus4       FollowUpStatus?
  followUpStatus5       FollowUpStatus?
  followUpStatus6       FollowUpStatus?
  followUpTime1         DateTime?
  followUpTime2         DateTime?
  followUpTime3         DateTime?
  followUpTime4         DateTime?
  followUpTime5         DateTime?
  followUpTime6         DateTime?
  isConversionLinkSent  Boolean                 @default(false)
  isIgnored             Boolean                 @default(false)
  isTakeControl         Boolean                 @default(false)
  messageCount          Int                     @default(0)
  aiSuspiciousFlags     Json?
  aiSuspiciousNotes     String?
  lastAiAlert           DateTime?
  lastSlackAlert        DateTime?
  slackChannelId        String?                 @db.VarChar(255)
  slackThreadTs         String?                 @db.VarChar(255)
  stage                 InstagramContactStage   @default(new)
  followerCount         Int?
  isUserFollowBusiness  Boolean?
  isBusinessFollowUser  Boolean?
  isVerifiedUser        Boolean?
  Organization          Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                  User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  InstagramConversation InstagramConversation[]
  InstagramFollowUp     InstagramFollowUp[]
  InstagramMessage      InstagramMessage[]
  StageChangeLog        StageChangeLog[]

  @@index([instagramNickname])
  @@index([stage])
  @@index([userId])
  @@index([organizationId])
}

model InstagramFollower {
  id                    String                    @id @default(uuid()) @db.Uuid
  userId                String                    @db.Uuid
  organizationId        String                    @db.Uuid
  instagramNickname     String
  instagramId           String?
  avatar                String?                   @db.VarChar(2048)
  followerCount         Int?
  isVerified            Boolean                   @default(false)
  batchNumber           Int                       @default(1)
  priority              InstagramFollowerPriority @default(normal)
  status                InstagramFollowerStatus   @default(pending)
  isTargeted            Boolean                   @default(false)
  lastResponseAt        DateTime?
  conversationStartedAt DateTime?
  followUpCount         Int                       @default(0)
  lastFollowUpAt        DateTime?
  nextFollowUpAt        DateTime?
  isConversationActive  Boolean                   @default(false)
  automationEnabled     Boolean                   @default(true)
  notes                 String?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  Organization          Organization              @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                  User                      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([instagramNickname])
  @@index([status])
  @@index([priority])
  @@index([batchNumber])
  @@index([userId])
  @@index([organizationId])
  @@index([automationEnabled])
}

model WorkHours {
  id             String         @id(map: "PK_WorkHours") @default(uuid()) @db.Uuid
  organizationId String         @db.Uuid
  dayOfWeek      DayOfWeek      @default(SUNDAY)
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  timeSlots      WorkTimeSlot[]

  @@index([organizationId], map: "IX_WorkHours_organizationId")
}

model WorkTimeSlot {
  id          String    @id(map: "PK_WorkTimeSlot") @default(uuid()) @db.Uuid
  workHoursId String    @db.Uuid
  start       DateTime  @db.Time(0)
  end         DateTime  @db.Time(0)
  workHours   WorkHours @relation(fields: [workHoursId], references: [id], onDelete: Cascade)

  @@index([workHoursId], map: "IX_WorkTimeSlot_workHoursId")
}

model AboutMeConfig {
  id                   String               @id @db.Uuid
  botSettingsId        String               @unique @db.Uuid
  aboutMePrompt        String
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)
}

model BotClosingLevel {
  id            String   @id @db.Uuid
  levelNumber   Int
  name          String
  description   String?
  promptContent String
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model BotMode {
  id                   String                 @id @db.Uuid
  name                 String
  description          String?
  promptTemplate       String
  isActive             Boolean                @default(true)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings[]
}

model BotRule {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  ruleType             String
  ruleContent          String
  isActive             Boolean              @default(true)
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model GlobalPrompt {
  id            String   @id @db.Uuid
  name          String
  promptContent String
  promptType    String
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model AdminPrompt {
  id              String   @id @default(uuid()) @db.Uuid
  generalPrompt   String
  technicalPrompt String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model AdminSettings {
  id                      String   @id @default(uuid()) @db.Uuid
  cacheForAllUsers        Boolean  @default(false)
  cacheType               String   @default("5m") // "5m" or "1h"
  enableConversationCache Boolean  @default(true)
  maxConversationMessages Int      @default(200)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
}

model BotStyle {
  id           String         @id @default(uuid()) @db.Uuid
  name         String
  description  String?
  promptText   String
  isDefault    Boolean        @default(false)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  PromptConfig PromptConfig[]
  TestSettings TestSettings[]
}

model PromptConfig {
  id                     String       @id @default(uuid()) @db.Uuid
  organizationId         String       @db.Uuid
  aboutUs                String?
  qualificationQuestions String?
  additionalInfo         String?
  botStyleId             String?      @db.Uuid
  youtubeLink            String?
  websiteLink            String?
  leadMagnetLink         String?
  conversionLink         String
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  botStyle               BotStyle?    @relation(fields: [botStyleId], references: [id])
  organization           Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([botStyleId])
}

model InstagramBotSettings {
  id                    String                  @id @db.Uuid
  userId                String                  @db.Uuid
  organizationId        String                  @db.Uuid
  instagramToken        String
  instagramAccountId    String
  status                InstagramBotStatus      @default(disabled)
  responseTimeMin       Int                     @default(10)
  responseTimeMax       Int                     @default(20)
  botModeId             String                  @db.Uuid
  createdAt             DateTime                @default(now())
  updatedAt             DateTime
  AboutMeConfig         AboutMeConfig?
  BotRule               BotRule[]
  BotMode               BotMode                 @relation(fields: [botModeId], references: [id])
  Organization          Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  User                  User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  LeadForm              LeadForm[]
  LeadMagnet            LeadMagnet[]
  QualificationQuestion QualificationQuestion[]

  @@unique([userId, organizationId])
  @@index([organizationId])
  @@index([userId])
}

model InstagramConversation {
  id                 String           @id @db.Uuid
  contactId          String           @db.Uuid
  messageType        MessageType
  messageContent     String
  instagramMessageId String?
  sentAt             DateTime
  createdAt          DateTime         @default(now())
  InstagramContact   InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
}

model InstagramMessage {
  id               String           @id @default(uuid()) @db.Uuid
  contactId        String           @db.Uuid
  messageId        String
  content          String
  isFromUser       Boolean          @default(true)
  mediaUrl         String?
  mediaType        String?
  mediaDescription String?
  timestamp        DateTime         @default(now())
  createdAt        DateTime         @default(now())
  InstagramContact InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
  @@index([messageId])
}

model InstagramFollowUp {
  id               String           @id @default(uuid()) @db.Uuid
  contactId        String           @db.Uuid
  sequenceNumber   Int
  message          String
  scheduledTime    DateTime
  status           FollowUpStatus   @default(pending)
  sentAt           DateTime?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  InstagramContact InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
}

model InstagramSettings {
  id                  String       @id @default(uuid()) @db.Uuid
  organizationId      String       @unique @db.Uuid
  isBotEnabled        Boolean      @default(false)
  minResponseTime     Int          @default(30)
  maxResponseTime     Int          @default(50)
  messageDelayMin     Int          @default(3)
  messageDelayMax     Int          @default(5)
  instagramToken      String?
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt
  followUpCleanupDays Int?         @default(30)
  autoCleanupEnabled  Boolean?     @default(true)
  Organization        Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model LeadForm {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  name                 String
  formFields           Json
  successMessage       String?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model LeadMagnet {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  name                 String
  description          String?
  contentUrl           String?
  triggerConditions    Json?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model QualificationQuestion {
  id                   String               @id @db.Uuid
  botSettingsId        String               @db.Uuid
  question             String
  orderNumber          Int
  isRequired           Boolean              @default(true)
  createdAt            DateTime             @default(now())
  updatedAt            DateTime
  InstagramBotSettings InstagramBotSettings @relation(fields: [botSettingsId], references: [id], onDelete: Cascade)

  @@index([botSettingsId])
}

model MessageQueue {
  id             String       @id @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  senderId       String
  messageId      String
  messageText    String
  mediaUrl       String?
  mediaType      String?
  priority       Int          @default(1)
  scheduledAt    DateTime
  attempts       Int          @default(0)
  maxAttempts    Int          @default(3)
  status         QueueStatus  @default(pending)
  errorMessage   String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  Organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([status, scheduledAt])
  @@index([organizationId])
}

model InstagramError {
  id             String        @id @default(uuid()) @db.Uuid
  organizationId String?       @db.Uuid
  contactId      String?       @db.Uuid
  messageId      String?
  operation      String
  errorMessage   String
  errorStack     String?
  retryCount     Int           @default(0)
  resolved       Boolean       @default(false)
  createdAt      DateTime      @default(now())
  Organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([createdAt])
  @@index([resolved])
}

model WebhookLog {
  id             String   @id @default(uuid()) @db.Uuid
  requestId      String   @unique
  method         String
  url            String
  headers        Json?
  body           Json?
  signature      String?
  isValid        Boolean
  responseCode   Int
  processingTime Int
  errorMessage   String?
  createdAt      DateTime @default(now())

  @@index([createdAt])
  @@index([isValid])
}

model StageChangeLog {
  id               String           @id @default(uuid()) @db.Uuid
  contactId        String           @db.Uuid
  fromStage        String?
  toStage          String
  reason           String?
  changedBy        String
  metadata         Json?
  createdAt        DateTime         @default(now())
  InstagramContact InstagramContact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
  @@index([createdAt])
  @@index([changedBy])
}

model TestSettings {
  id                     String       @id @default(uuid()) @db.Uuid
  organizationId         String       @db.Uuid
  userId                 String       @db.Uuid
  aboutUs                String?
  qualificationQuestions String?
  additionalInfo         String?
  botStyleId             String?      @db.Uuid
  youtubeLink            String?
  websiteLink            String?
  leadMagnetLink         String?
  conversionLink         String?
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  botStyle               BotStyle?    @relation(fields: [botStyleId], references: [id])
  organization           Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user                   User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@index([organizationId])
  @@index([userId])
  @@index([botStyleId])
}

model TestAdminPrompt {
  id              String       @id @default(uuid()) @db.Uuid
  organizationId  String       @db.Uuid
  userId          String       @db.Uuid
  generalPrompt   String
  technicalPrompt String
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user            User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@index([organizationId])
  @@index([userId])
}

enum ActionType {
  CREATE @map("create")
  UPDATE @map("update")
  DELETE @map("delete")
}

enum ActorType {
  SYSTEM @map("system")
  MEMBER @map("member")
  API    @map("api")
}

enum ContactRecord {
  PERSON  @map("person")
  COMPANY @map("company")
}

enum ContactStage {
  LEAD           @map("lead")
  QUALIFIED      @map("qualified")
  OPPORTUNITY    @map("opportunity")
  PROPOSAL       @map("proposal")
  IN_NEGOTIATION @map("inNegotiation")
  LOST           @map("lost")
  WON            @map("won")
}

enum ContactTaskStatus {
  OPEN      @map("open")
  COMPLETED @map("completed")
}

enum DayOfWeek {
  SUNDAY    @map("sunday")
  MONDAY    @map("monday")
  TUESDAY   @map("tuesday")
  WEDNESDAY @map("wednesday")
  THURSDAY  @map("thursday")
  FRIDAY    @map("friday")
  SATURDAY  @map("saturday")
}

enum FeedbackCategory {
  SUGGESTION @map("suggestion")
  PROBLEM    @map("problem")
  QUESTION   @map("question")
}

enum InvitationStatus {
  PENDING  @map("pending")
  ACCEPTED @map("accepted")
  REVOKED  @map("revoked")
}

enum Role {
  MEMBER @map("member")
  ADMIN  @map("admin")
}

enum WebhookTrigger {
  CONTACT_CREATED            @map("contactCreated")
  CONTACT_UPDATED            @map("contactUpdated")
  CONTACT_DELETED            @map("contactDeleted")
  INSTAGRAM_MESSAGE_RECEIVED @map("instagramMessageReceived")
}

enum FollowUpStatus {
  pending
  sent
  failed
  external
}

enum InstagramBotStatus {
  active
  paused
  disabled
}

enum InstagramContactStage {
  new
  initial
  engaged
  qualified
  formsent
  disqualified
  converted
  blocked
  suspicious
}

enum MessageType {
  incoming
  outgoing
}

enum QueueStatus {
  pending
  processing
  completed
  failed
  retrying
}

enum InstagramFollowerPriority {
  low
  normal
  high
  urgent
}

enum InstagramFollowerStatus {
  pending
  contacted
  responded
  engaged
  converted
  ignored
  blocked
}

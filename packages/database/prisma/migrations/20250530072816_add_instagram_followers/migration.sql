-- CreateEnum
CREATE TYPE "InstagramFollowerPriority" AS ENUM ('low', 'normal', 'high', 'urgent');

-- CreateEnum
CREATE TYPE "InstagramFollowerStatus" AS ENUM ('pending', 'contacted', 'responded', 'engaged', 'converted', 'ignored', 'blocked');

-- CreateTable
CREATE TABLE "InstagramFollower" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramNickname" TEXT NOT NULL,
    "instagramId" TEXT,
    "avatar" VARCHAR(2048),
    "followerCount" INTEGER,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "batchNumber" INTEGER NOT NULL DEFAULT 1,
    "priority" "InstagramFollowerPriority" NOT NULL DEFAULT 'normal',
    "status" "InstagramFollowerStatus" NOT NULL DEFAULT 'pending',
    "isTargeted" BOOLEAN NOT NULL DEFAULT false,
    "lastResponseAt" TIMESTAMP(3),
    "conversationStartedAt" TIMESTAMP(3),
    "followUpCount" INTEGER NOT NULL DEFAULT 0,
    "lastFollowUpAt" TIMESTAMP(3),
    "nextFollowUpAt" TIMESTAMP(3),
    "isConversationActive" BOOLEAN NOT NULL DEFAULT false,
    "automationEnabled" BOOLEAN NOT NULL DEFAULT true,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramFollower_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "InstagramFollower_instagramNickname_idx" ON "InstagramFollower"("instagramNickname");

-- CreateIndex
CREATE INDEX "InstagramFollower_status_idx" ON "InstagramFollower"("status");

-- CreateIndex
CREATE INDEX "InstagramFollower_priority_idx" ON "InstagramFollower"("priority");

-- CreateIndex
CREATE INDEX "InstagramFollower_batchNumber_idx" ON "InstagramFollower"("batchNumber");

-- CreateIndex
CREATE INDEX "InstagramFollower_userId_idx" ON "InstagramFollower"("userId");

-- CreateIndex
CREATE INDEX "InstagramFollower_organizationId_idx" ON "InstagramFollower"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramFollower_automationEnabled_idx" ON "InstagramFollower"("automationEnabled");

-- AddForeignKey
ALTER TABLE "InstagramFollower" ADD CONSTRAINT "InstagramFollower_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramFollower" ADD CONSTRAINT "InstagramFollower_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
